import os
import shutil
import json
import random
import sys
from pathlib import Path
from tkinter import Tk, filedialog, messagebox

# Vérifier que PIL est disponible
try:
    from PIL import Image
except ImportError:
    messagebox.showerror("Erreur", "Le module Pillow (PIL) est requis. Installez-le avec 'pip install pillow'.")
    sys.exit(1)

CONFIG_FILE = "last_paths_correspondants.json"

def charger_chemins():
    chemins_par_defaut = {
        "source": str(Path.home()),
        "reference": str(Path.home()),
        "destination_c": str(Path.home()),
        "destination_d": str(Path.home())
    }
    if Path(CONFIG_FILE).exists():
        try:
            with open(CONFIG_FILE, "r") as f:
                chemins = json.load(f)
                for key in chemins_par_defaut:
                    if key not in chemins:
                        chemins[key] = chemins_par_defaut[key]
                return chemins
        except:
            return chemins_par_defaut
    return chemins_par_defaut

def sauvegarder_chemins(source, reference, destination_c, destination_d):
    chemins = {
        "source": str(source),
        "reference": str(reference),
        "destination_c": str(destination_c),
        "destination_d": str(destination_d)
    }
    with open(CONFIG_FILE, "w") as f:
        json.dump(chemins, f)

def obtenir_resolution(fichier):
    """Obtient la résolution d'un fichier image."""
    try:
        with Image.open(fichier) as img:
            return img.size  # (largeur, hauteur)
    except Exception as e:
        print(f"Erreur lors de la lecture de {fichier}: {e}")
        return None

def copier_fichiers_par_resolution():
    root = Tk()
    root.withdraw()
    chemins = charger_chemins()

    # Sélection du dossier source (A)
    dossier_a_str = filedialog.askdirectory(title="Sélectionner le dossier source (A)", initialdir=chemins["source"])
    if not dossier_a_str:  # L'utilisateur a annulé
        return False
    dossier_a = Path(dossier_a_str)

    # Sélection du dossier de référence (B)
    dossier_b_str = filedialog.askdirectory(title="Sélectionner le dossier de référence (B)", initialdir=chemins["reference"])
    if not dossier_b_str:  # L'utilisateur a annulé
        return False
    dossier_b = Path(dossier_b_str)

    # Sélection du dossier destination C
    dossier_c_str = filedialog.askdirectory(title="Sélectionner le dossier destination C", initialdir=chemins["destination_c"])
    if not dossier_c_str:  # L'utilisateur a annulé
        return False
    dossier_c = Path(dossier_c_str)

    # Sélection du dossier destination D
    dossier_d_str = filedialog.askdirectory(title="Sélectionner le dossier destination D", initialdir=chemins["destination_d"])
    if not dossier_d_str:  # L'utilisateur a annulé
        return False
    dossier_d = Path(dossier_d_str)
    # Sauvegarder les chemins sélectionnés
    sauvegarder_chemins(dossier_a, dossier_b, dossier_c, dossier_d)

    # Vérifier que les dossiers source existent
    if not dossier_a.exists():
        messagebox.showerror("Erreur", f"Le dossier source A n'existe pas: {dossier_a}")
        return False
    if not dossier_b.exists():
        messagebox.showerror("Erreur", f"Le dossier de référence B n'existe pas: {dossier_b}")
        return False

    prefixe = dossier_a.name + "_"

    # Créer les dossiers de destination s'ils n'existent pas
    try:
        dossier_c.mkdir(parents=True, exist_ok=True)
        dossier_d.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        messagebox.showerror("Erreur", f"Impossible de créer les dossiers de destination: {e}")
        return False

    # Obtenir la liste des fichiers dans le dossier B
    try:
        fichiers_b = {f.name for f in dossier_b.iterdir() if f.is_file()}
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors de la lecture du dossier B: {e}")
        return False

    if not fichiers_b:
        messagebox.showwarning("Attention", f"Aucun fichier trouvé dans le dossier de référence B: {dossier_b}")
        return False

    # Grouper les fichiers de A par résolution
    groupes = {}
    fichiers_traites = 0
    fichiers_avec_erreur = 0

    try:
        for fichier in dossier_a.iterdir():
            if fichier.is_file():
                fichiers_traites += 1
                res = obtenir_resolution(fichier)
                if res:
                    groupes.setdefault(res, []).append(fichier)
                else:
                    fichiers_avec_erreur += 1
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur lors de la lecture du dossier A: {e}")
        return False

    if not groupes:
        messagebox.showwarning("Attention", f"Aucune image valide trouvée dans le dossier source A: {dossier_a}")
        return False

    print(f"Fichiers traités: {fichiers_traites}, Fichiers avec erreur: {fichiers_avec_erreur}")
    print(f"Groupes de résolution trouvés: {len(groupes)}")

    fichiers_copies_c = 0
    fichiers_copies_d = 0
    erreurs_copie = 0

    for res, fichiers in groupes.items():
        # Filtrer pour ne garder que ceux qui existent aussi dans B
        fichiers_avec_jumeau = [f for f in fichiers if f.name in fichiers_b]
        if not fichiers_avec_jumeau:
            print(f"Aucun fichier correspondant trouvé pour la résolution {res}")
            continue

        # Sélectionner jusqu'à 3 fichiers aléatoirement
        selection = random.sample(fichiers_avec_jumeau, min(3, len(fichiers_avec_jumeau)))
        print(f"Résolution {res}: {len(fichiers_avec_jumeau)} fichiers correspondants, {len(selection)} sélectionnés")

        for fichier in selection:
            try:
                # Copier le fichier du dossier A vers C
                nouveau_nom = f"{prefixe}{fichier.name}"
                destination = dossier_c / nouveau_nom
                shutil.copy2(fichier, destination)
                fichiers_copies_c += 1

                # Copier le fichier correspondant du dossier B vers D
                fichier_b = dossier_b / fichier.name
                if fichier_b.exists():
                    destination_d = dossier_d / nouveau_nom
                    shutil.copy2(fichier_b, destination_d)
                    fichiers_copies_d += 1
                else:
                    print(f"Attention: Le fichier {fichier_b} n'existe pas dans le dossier B")
                    erreurs_copie += 1

            except Exception as e:
                print(f"Erreur lors de la copie de {fichier}: {e}")
                erreurs_copie += 1
    # Afficher le résultat
    message = (f"Opération terminée.\n"
               f"Fichiers traités: {fichiers_traites}\n"
               f"Groupes de résolution: {len(groupes)}\n"
               f"Fichiers copiés dans C: {fichiers_copies_c}\n"
               f"Fichiers copiés dans D: {fichiers_copies_d}\n")

    if erreurs_copie > 0:
        message += f"Erreurs de copie: {erreurs_copie}\n"

    if fichiers_avec_erreur > 0:
        message += f"Fichiers non lisibles: {fichiers_avec_erreur}\n"

    message += (f"\nVers les dossiers:\n"
                f"C: {dossier_c}\n"
                f"D: {dossier_d}")

    messagebox.showinfo("Résultat", message)
    return True

if __name__ == "__main__":
    copier_fichiers_par_resolution()